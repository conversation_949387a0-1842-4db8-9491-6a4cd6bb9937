from flask import Flask, request
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_seasurf import <PERSON><PERSON>ur<PERSON>
from flask_caching import Cache
from flask_marshmallow import Marshmallow
# SocketIO removed
from app.config import Config
import firebase_admin
from firebase_admin import credentials
import logging
import os

db = SQLAlchemy()
ma = Marshmallow()
csrf = SeaSurf()
# Initialize cache with default config, will be configured in create_app
cache = Cache()

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Configure logging
    log_dir = os.path.dirname(app.config["LOG_FILE"])
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(app.config["LOG_FILE"]),
            logging.<PERSON><PERSON>and<PERSON>()
        ]
    )

    # Set encryption module to DEBUG level for more detailed logs
    encryption_logger = logging.getLogger('app.utils.encryption')
    encryption_logger.setLevel(logging.DEBUG)

    # Initialize extensions
    db.init_app(app)
    ma.init_app(app)

    # Configure CSRF protection
    # Re-enable CSRF protection
    app.config['CSRF_DISABLE'] = False
    # Initialize CSRF protection
    csrf.init_app(app)

    # Configure cache based on environment
    if app.config.get('CACHE_TYPE') == 'redis':
        app.config['CACHE_REDIS_URL'] = app.config.get('REDIS_URL', 'redis://localhost:6379/0')
        app.config['CACHE_DEFAULT_TIMEOUT'] = app.config.get('CACHE_TIMEOUT', 3600)  # 1 hour default
    else:
        # Fallback to simple cache for development
        app.config['CACHE_TYPE'] = 'SimpleCache'
        app.config['CACHE_DEFAULT_TIMEOUT'] = 300  # 5 minutes

    # Initialize cache
    cache.init_app(app)

    # Initialize custom rate limiter (no additional setup needed)
    # Rate limiting is handled by our custom decorator in utils/custom_rate_limiter.py
    logging.info("Custom rate limiter initialized")

    # Set up periodic cleanup for rate limiter (every hour)
    import threading
    from app.utils.custom_rate_limiter import cleanup_rate_limiter

    def periodic_cleanup():
        """Periodically clean up expired rate limit windows."""
        cleanup_rate_limiter()
        # Schedule next cleanup in 1 hour
        timer = threading.Timer(3600.0, periodic_cleanup)
        timer.daemon = True
        timer.start()

    # Start the cleanup timer
    periodic_cleanup()

    # Configure CORS - support both development and production origins
    allowed_origins = []

    # Development origins
    dev_origins = ["https://localhost:5173", "http://localhost:5173"]
    allowed_origins.extend(dev_origins)

    # Production origins - get from environment variables
    frontend_url = os.getenv("FRONTEND_URL")
    if frontend_url:
        allowed_origins.append(frontend_url)
        # Also add without trailing slash if it has one
        if frontend_url.endswith('/'):
            allowed_origins.append(frontend_url.rstrip('/'))
        else:
            allowed_origins.append(frontend_url + '/')

    # Firebase hosting domain (if different from FRONTEND_URL)
    firebase_domain = os.getenv("FIREBASE_HOSTING_DOMAIN")
    if firebase_domain and firebase_domain not in allowed_origins:
        allowed_origins.append(firebase_domain)
        if firebase_domain.endswith('/'):
            allowed_origins.append(firebase_domain.rstrip('/'))
        else:
            allowed_origins.append(firebase_domain + '/')

    logging.info(f"CORS allowed origins: {allowed_origins}")

    CORS(app,
         supports_credentials=True, # Enable credentials for CSRF token support
         resources={r"/*": {"origins": allowed_origins}},
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Authorization", "Content-Type", "X-CSRFToken", "Accept", "Origin"],
         expose_headers=["Content-Type", "X-CSRFToken", "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials"],
         vary_header=True)

    # Initialize Firebase
    # Check if we have JSON credentials directly or need to load from file
    firebase_initialized = False
    try:
        if hasattr(app.config, 'FIREBASE_CREDENTIALS') and app.config.FIREBASE_CREDENTIALS:
            # Use the parsed JSON credentials
            cred = credentials.Certificate(app.config.FIREBASE_CREDENTIALS)
            firebase_admin.initialize_app(cred, {
                'storageBucket': app.config["FIREBASE_STORAGE_BUCKET"]
            })
            firebase_initialized = True
            logging.info("Firebase initialized with JSON credentials")
        elif app.config.get("FIREBASE_CREDENTIALS_PATH"):
            # Use the credentials file path
            cred = credentials.Certificate(app.config["FIREBASE_CREDENTIALS_PATH"])
            firebase_admin.initialize_app(cred, {
                'storageBucket': app.config["FIREBASE_STORAGE_BUCKET"]
            })
            firebase_initialized = True
            logging.info("Firebase initialized with credentials file")
        else:
            logging.warning("No Firebase credentials provided - Firebase features will be disabled")
    except Exception as e:
        logging.error(f"Failed to initialize Firebase: {e}")
        logging.warning("Firebase features will be disabled")

    # Store Firebase initialization status in app config
    app.config['FIREBASE_INITIALIZED'] = firebase_initialized

    # Register blueprints
    from app.controllers.auth_controller import auth_bp
    from app.controllers.user_controller import user_bp
    from app.controllers.customer_controller import customer_bp
    from app.controllers.event_controller import event_bp
    from app.controllers.document_controller import document_bp
    # Session controller removed
    from app.controllers.audit_controller import audit_bp
    from app.controllers.export_controller import export_bp
    from app.controllers.customer_note_controller import customer_note_bp
    from app.controllers.document_template_controller import document_template_bp
    from app.controllers.product_controller import product_bp
    from app.controllers.quotation_controller import quotation_bp
    from app.controllers.time_tracking_controller import time_tracking_bp

    app.register_blueprint(auth_bp, url_prefix="/api/auth")
    app.register_blueprint(user_bp, url_prefix="/api/users")
    app.register_blueprint(customer_bp, url_prefix="/api/customers")
    app.register_blueprint(event_bp, url_prefix="/api/events")
    app.register_blueprint(document_bp, url_prefix="/api/documents")
    app.register_blueprint(audit_bp, url_prefix="/api/audit")
    app.register_blueprint(export_bp, url_prefix="/api/export")
    app.register_blueprint(customer_note_bp, url_prefix="/api/customer-notes")
    app.register_blueprint(document_template_bp, url_prefix="/api/document-templates")
    app.register_blueprint(product_bp, url_prefix="/api/products")
    app.register_blueprint(quotation_bp, url_prefix="/api/quotations")
    app.register_blueprint(time_tracking_bp, url_prefix="/api/time-tracking")

    # Add health check endpoint
    @app.route('/api/health')
    def health_check():
        """Health check endpoint for monitoring services."""
        firebase_status = app.config.get('FIREBASE_INITIALIZED', False)
        return {
            'status': 'healthy',
            'message': 'Customer Management API is running',
            'firebase_initialized': firebase_status
        }, 200

    # Schedule periodic tasks
    from flask_apscheduler import APScheduler
    scheduler = APScheduler()
    scheduler.init_app(app)
    scheduler.start()

    # SocketIO initialization removed
    socketio = None

    # Import all models to ensure they are registered with SQLAlchemy
    from app.models import User, Customer, Event, Document, AuditLog, CustomerNote, Product, Quotation, QuotationItem, TimeEntry, MileageEntry

    # Create database tables
    with app.app_context():
        db.create_all()

    # CLI commands for key rotation removed - no longer needed with Render's encryption at rest

    # Import the response sanitizer
    from app.utils.response_sanitizer import sanitize_response

    # Add security headers and sanitize responses
    @app.after_request
    def process_response(response):
        # Check if this is a file download response
        is_file_download = (
            response.headers.get('Content-Disposition', '').startswith('attachment') or
            (request.endpoint and 'file' in request.endpoint)
        )

        # Prevent browsers from detecting the MIME type of a response
        response.headers['X-Content-Type-Options'] = 'nosniff'

        # Prevent the page from being framed (clickjacking protection)
        response.headers['X-Frame-Options'] = 'DENY'

        # Enable XSS protection in browsers
        response.headers['X-XSS-Protection'] = '1; mode=block'

        # Content Security Policy to restrict what can be loaded
        # Allow blob: for file downloads and support production domains
        connect_src_domains = [
            "'self'",
            "https://localhost:5173",
            "https://identitytoolkit.googleapis.com",
            "https://securetoken.googleapis.com"
        ]

        # Add production frontend domains to CSP
        if frontend_url:
            connect_src_domains.append(frontend_url.rstrip('/'))
        if firebase_domain:
            connect_src_domains.append(firebase_domain.rstrip('/'))

        csp_policy = f"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src {' '.join(connect_src_domains)}; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; frame-ancestors 'none';"
        response.headers['Content-Security-Policy'] = csp_policy

        # Only apply restrictive cache headers to non-file responses
        if not is_file_download:
            # Prevent sensitive information from being cached
            response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'

        # Sanitize the response to remove sensitive information
        if app.config.get('SANITIZE_RESPONSES', True):
            response = sanitize_response(response)

        return response

    return app, socketio