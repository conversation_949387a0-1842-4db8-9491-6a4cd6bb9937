#!/bin/bash
set -e

echo "=== Starting Customer Management API ==="

# Set environment variables
export FLASK_ENV=production
export FLASK_DEBUG=False

# Debug information
echo "Current directory: $(pwd)"
echo "Python version: $(python --version)"
echo "Files in current directory:"
ls -la

# Test if we can import the app
echo "Testing app import..."
python -c "
try:
    import app
    print('✓ Successfully imported app module')
    print('✓ app.app exists:', hasattr(app, 'app'))
    if hasattr(app, 'app'):
        print('✓ app.app type:', type(app.app))
except Exception as e:
    print('✗ Failed to import app:', e)
    import traceback
    traceback.print_exc()
    exit(1)
"

# Initialize database if environment variables are available
if [ -n "$DATABASE_URL" ] && [ -n "$SECRET_KEY" ]; then
    echo "Initializing production database..."
    python init_production_db.py || echo "Database initialization failed, continuing..."
else
    echo "Skipping database initialization (missing environment variables)"
fi

# Start the application
echo "Starting Flask application with Gun<PERSON>..."
exec gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 --access-logfile - --error-logfile - app:app
